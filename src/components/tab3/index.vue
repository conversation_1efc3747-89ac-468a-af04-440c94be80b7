<template>
    <div
        ref="scrollContainer"
        class="mb-100 flex flex-col items-center"
        @scroll="handleScroll">
        <!-- 子导航 -->
        <sub-nav-tabs
            v-model="rankStore.currentType"
            class="pb-13"
            :nav-config="navConfig"
            @change="handleTypeChange"
        />

        <div
            v-if="rankStore.currentType === RANK_TYPE.TAB1"
            class="bg-default h-[452.5px] w-[375px] flex flex-col items-center"
            :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
        >
            <div class="h-[12px] pt-7 text-left text-[12px] text-[#ffebc7] leading-[12px]">累计多日参与星动挑战打分可得奖励</div>

            <!-- 任务进度区域 -->
            <div class="relative w-[320px] flex">
                <div
                    class="bg-default z-1 mt-[14px] h-[95.5px] w-[52px] flex flex-col items-center"
                    :style="{ backgroundImage: `url(${requireImg('tab3/<EMAIL>')})` }"
                >
                    <img
                        class="h-[51px] w-[51px] border-[2px] border-[#dad1ff] rounded-full bg-[#aca6c1]"
                        :src="getAvatar(audioPlayer?.user?.username)"
                        alt="">
                    <div class="mt-4 text-12 text-[#FFEF67] leading-12"> <span class="text-15">{{ audioPlayer?.value }}</span> 天</div>
                    <div class="mt-6 text-[11px] text-[#ADA5CC] leading-11">已参与</div>
                </div>
                <ChallengeProgress
                    class="ml-[-8px] mt-[56px]"
                    :value="audioPlayer.value"
                    :done-icon="requireImg('tab3/<EMAIL>')"
                    :un-done-icon="requireImg('tab3/<EMAIL>')"
                    :targets="[
                        {
                            value: 100,
                            rewards: [{ ...getRewardDataInfo('A24'), num: 1 }],
                        },
                        {
                            value: 300,
                            rewards: [{ ...getRewardDataInfo('A25'), num: 3 }],
                        },
                        {
                            value: 600,
                            rewards: [{ ...getRewardDataInfo('A26'), num: 1 }],

                        },
                    ]"
                />
            </div>

            <!-- 打分区域 -->
            <div class="mt-6 w-[320px] flex-1 px-4">
                <!-- 主播信息卡片 -->
                <div class="mb-4 rounded-lg bg-black bg-opacity-30 p-4">
                    <!-- 主播头像和信息 -->
                    <div class="mb-3 flex items-center">
                        <img
                            :src="showAnchorInfo ? getAvatar(audioPlayer.anchorWork?.anchor?.username) : requireImg('default_avatar_no_compress.png')"
                            class="mr-3 h-[50px] w-[50px] rounded-full"
                            :alt="showAnchorInfo ? audioPlayer.anchorWork?.anchor?.nickname : '神秘主播'"
                        />
                        <div class="flex-1">
                            <div class="mb-1 text-[16px] text-[#ffebc7] font-bold">
                                {{ showAnchorInfo ? audioPlayer.anchorWork?.anchor?.nickname : '神秘主播' }}
                            </div>
                            <div
                                v-if="showAnchorInfo"
                                class="mb-1 text-[12px] text-[#999]">
                                {{ audioPlayer.anchorWork?.workTitle || '精彩作品' }}
                            </div>
                            <div
                                v-if="showAnchorInfo"
                                class="line-clamp-2 text-[11px] text-[#ccc]">
                                {{ audioPlayer.anchorWork?.introduction }}
                            </div>
                        </div>
                    </div>

                    <!-- 音频播放器 -->
                    <div class="mb-4">
                        <!-- 播放按钮 -->
                        <div class="mb-3 flex items-center justify-center">
                            <div
                                class="h-[60px] w-[60px] flex cursor-pointer items-center justify-center rounded-full bg-[#ffeb3b]"
                                :class="{ 'opacity-50 cursor-not-allowed': !canStartPlaying }"
                                @click="handlePlayToggle"
                            >
                                <div
                                    v-if="audioPlayer.playing"
                                    class="text-[24px] text-black">
                                    ⏸
                                </div>
                                <div
                                    v-else
                                    class="ml-1 text-[24px] text-black">
                                    ▶
                                </div>
                            </div>
                        </div>

                        <!-- 进度条 -->
                        <div class="relative mb-2">
                            <div class="h-[4px] w-full rounded-full bg-[#333]"></div>
                            <div
                                class="absolute left-0 top-0 h-[4px] rounded-full bg-[#ffeb3b] transition-all duration-300"
                                :style="{ width: `${audioProgress}%` }"
                            ></div>
                        </div>
                        <div class="flex justify-between text-[10px] text-[#999]">
                            <span>{{ formatTime(currentTime) }}</span>
                            <span>{{ formatTime(duration) }}</span>
                        </div>
                    </div>

                    <!-- 评分区域 -->
                    <div class="mb-4">
                        <!-- 倒计时提示 -->
                        <div
                            v-if="countdown > 0"
                            class="mb-3 text-center">
                            <div class="mb-1 text-[14px] text-[#ff9800]">请先听完音频</div>
                            <div class="text-[12px] text-[#999]">剩余倒计时：{{ countdown }}s</div>
                        </div>

                        <!-- 可以打分提示 -->
                        <div
                            v-else-if="!hasScored"
                            class="mb-3 text-center">
                            <div class="text-[14px] text-[#4caf50]">现可进行打分</div>
                        </div>

                        <!-- 评分条 -->
                        <div class="mb-3 flex items-center justify-between">
                            <div
                                v-for="score in 5"
                                :key="score"
                                class="h-[30px] w-[50px] flex cursor-pointer items-center justify-center rounded-full transition-all"
                                :class="[
                                    countdown > 0 ? 'bg-[#333] cursor-not-allowed' : 'bg-[#666] hover:bg-[#888]',
                                    selectedScore === score ? 'bg-[#ffeb3b] text-black' : 'text-white',
                                ]"
                                @click="handleScoreSelect(score)"
                            >
                                {{ score }}
                            </div>
                        </div>

                        <!-- 打分按钮 -->
                        <div class="text-center">
                            <div
                                class="inline-block cursor-pointer rounded-full px-6 py-2 text-[14px] font-bold transition-all"
                                :class="canSubmitScore ? 'bg-[#ffeb3b] text-black' : 'bg-[#333] text-[#666] cursor-not-allowed'"
                                @click="handleSubmitScore"
                            >
                                {{ hasScored ? '已打分' : '确认打分' }}
                            </div>
                        </div>
                    </div>

                    <!-- 打分结果 -->
                    <div
                        v-if="hasScored"
                        class="text-center">
                        <div
                            class="mb-2 text-[16px] font-bold"
                            :class="audioPlayer.notLike ? 'text-[#ff5722]' : 'text-[#4caf50]'">
                            {{ audioPlayer.notLike ? '磁场不契合' : '怦然心动' }}
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex justify-center gap-3">
                            <div
                                v-if="audioPlayer.notLike"
                                class="cursor-pointer rounded-full bg-[#ffeb3b] px-4 py-2 text-[12px] text-black font-bold"
                                @click="handleNextChallenge"
                            >
                                挑战下一个
                            </div>
                            <template v-else>
                                <div
                                    v-if="audioPlayer.anchorWork?.channelId"
                                    class="cursor-pointer rounded-full bg-[#4caf50] px-4 py-2 text-[12px] text-white font-bold"
                                    @click="handleGoToRoom"
                                >
                                    去看看
                                </div>
                                <div
                                    v-else
                                    class="cursor-pointer rounded-full bg-[#2196f3] px-4 py-2 text-[12px] text-white font-bold"
                                    @click="handleFollow"
                                >
                                    {{ isFollowed ? '已关注' : '关注' }}
                                </div>
                                <div
                                    class="cursor-pointer rounded-full bg-[#ffeb3b] px-4 py-2 text-[12px] text-black font-bold"
                                    @click="handleNextChallenge"
                                >
                                    下一个
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 榜单内容 -->
        <div
            v-if="rankStore.currentType === RANK_TYPE.TAB2"
            class="rank-content">
            <div class="w-full flex items-center justify-center text-13 text-[#FFFFFF]">
                开启榜单后送出1豆/在房1分钟=1榜单值 <img
                    class="ml-2 h-16 w-16"
                    src="@/assets/img/tab3/<EMAIL>"
                    alt="">
            </div>
            <!-- 错误状态 -->
            <div
                v-if="rankStore.error.hasError"
                class="error-state">
                <div class="error-icon">⚠️</div>
                <div class="error-message">{{ rankStore.error.message }}</div>
                <div
                    v-if="rankStore.error.canRetry"
                    class="retry-btn"
                    @click="handleRetry"
                >
                    重新加载
                </div>
            </div>

            <!-- 正常内容 -->
            <template v-else>
                <!-- 前三名 -->
                <top-three
                    v-if="rankStore.topThreeList.length > 0"
                    :list="rankStore.topThreeList"
                    :type="rankStore.currentType"
                />

                <!-- 普通排名列表 -->
                <div
                    v-if="rankStore.normalList.length > 0"
                    class="normal-list">
                    <rank-item
                        v-for="(item, index) in rankStore.normalList"
                        :key="`${rankStore.currentType}-${item.uid || item.id}-${index}`"
                        :item="item"
                        :rank="index + 4"
                        :type="rankStore.currentType"
                    />
                </div>

                <!-- 自己的排名 -->
                <div
                    v-if="rankStore.hasSelfRank"
                    class="self-rank">
                    <rank-item
                        :item="rankStore.selfRank"
                        :rank="rankStore.selfRank.rank || rankStore.selfRank.ranking"
                        :type="rankStore.currentType"
                        :is-self="true"
                    />
                </div>

                <!-- 空状态 -->
                <div
                    v-if="rankStore.showEmptyState"
                    class="empty-state">
                    <div class="empty-text">暂无榜单数据</div>
                </div>

                <!-- 加载更多提示 -->
                <div
                    v-if="rankStore.loading.more"
                    class="loading-more">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>

                <!-- 没有更多数据 -->
                <div
                    v-if="!rankStore.hasMore && rankStore.list.length > 0"
                    class="no-more">
                    <div class="no-more-line"></div>
                    <span>没有更多数据了</span>
                    <div class="no-more-line"></div>
                </div>
            </template>
        </div>

        <common-copyright class="mt-10"></common-copyright>
    </div>
</template>

<script setup>
import { throttle } from 'lodash-es';
import SubNavTabs from './sub-nav-tabs.vue';
import ChallengeProgress from './challenge-progress.vue';
import useRankStore, { RANK_TYPE } from './use-rank-store';
import useAudioPlayer from './use-audio-player';
import useLoading from '@/hooks/use-loading';
import { getRewardDataInfo } from '@/utils';

const rankStore = useRankStore();
const scrollContainer = ref(null);
const audioPlayer = useAudioPlayer();

// 音频播放状态
const currentTime = ref(0);
const duration = ref(0);
const audioProgress = ref(0);

// 打分状态
const selectedScore = ref(0);
const hasScored = ref(false);
const countdown = ref(10);
const isFollowed = ref(false);

// 是否可以开始播放
const canStartPlaying = computed(() => true);

// 是否显示主播信息
const showAnchorInfo = computed(() => hasScored.value);

// 是否可以提交评分
const canSubmitScore = computed(() => selectedScore.value > 0 && countdown.value === 0 && !hasScored.value);

// 导航配置
const navConfig = computed(() => [
    {
        value: RANK_TYPE.TAB1,
        label: '星动挑战',
    },
    {
        value: RANK_TYPE.TAB2,
        label: '星动指数榜',
    },
]);

useLoading(computed(() => rankStore.loading.list));

// 格式化时间
function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// 播放/暂停切换
function handlePlayToggle() {
    if (!canStartPlaying.value)
        return;

    if (audioPlayer.playing) {
        audioPlayer.pauseAudio();
    }
    else {
        // 这里需要音频URL，从audioPlayer.anchorWork.workLink获取
        if (audioPlayer.anchorWork?.workLink) {
            audioPlayer.playAudio(audioPlayer.anchorWork.workLink);
        }
    }
}

// 选择评分
function handleScoreSelect(score) {
    if (countdown.value > 0)
        return;
    selectedScore.value = score;
}

// 提交评分
async function handleSubmitScore() {
    if (!canSubmitScore.value)
        return;

    try {
        await audioPlayer.rate(selectedScore.value);
        hasScored.value = true;
    }
    catch (error) {
        console.error('提交评分失败:', error);
    }
}

// 下一个挑战
async function handleNextChallenge() {
    // 重置状态
    selectedScore.value = 0;
    hasScored.value = false;
    countdown.value = 10;
    isFollowed.value = false;

    // 获取新的挑战数据
    await audioPlayer.getNewWorker();
}

// 关注主播
function handleFollow() {
    isFollowed.value = true;
    // 这里可以调用关注API
}

// 进入直播间
function handleGoToRoom() {
    // 这里可以跳转到直播间
    if (audioPlayer.anchorWork?.channelId) {
        // 跳转到直播间的逻辑
        // window.location.href = `/room/${audioPlayer.anchorWork.channelId}`;
    }
}

// 切换榜单类型
const handleTypeChange = async (type) => {
    await rankStore.switchRankType(type);
};

// 重试加载
const handleRetry = async () => {
    await rankStore.retryQuery();
};

// 滚动加载更多
const handleScroll = throttle(() => {
    const container = scrollContainer.value;
    if (!container || rankStore.loading.more || !rankStore.hasMore || rankStore.error.hasError)
        return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    if (scrollTop + clientHeight >= scrollHeight - 50) {
        rankStore.loadMore();
    }
}, 300);

onMounted(async () => {
    rankStore.switchRankType(RANK_TYPE.TAB1);
    await rankStore.retryQuery();

    // 获取星动挑战数据
    await audioPlayer.getNewWorker();
});
</script>

<style lang="less" scoped>
.rank-list {
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    // background: url('@/assets/img/<EMAIL>') #244f8e no-repeat center top;
    width: 375px;
    background: #242031;
    min-height: 449px;
    padding-top: 12px;

    .rank-content {
        margin-top: 15px;

        .error-state {
            text-align: center;
            padding: 60px 20px;

            .error-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .error-message {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 20px;
                line-height: 1.4;
            }

            .retry-btn {
                display: inline-block;
                padding: 10px 20px;
                background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
                color: #fff;
                border-radius: 20px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.95);
                    background: linear-gradient(135deg, #ff5252, #ff7979);
                }
            }
        }

        .normal-list {
            margin-top: 0px;
        }

        .self-rank {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);

            .self-rank-title {
                font-size: 16px;
                color: #fff;
                margin-bottom: 10px;
                text-align: center;
            }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;

            .empty-icon {
                font-size: 48px;
                margin-bottom: 15px;
            }

            .empty-text {
                font-size: 16px;
                color: #fff;
                margin-bottom: 8px;
            }

            .empty-tip {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .loading-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            gap: 8px;

            .loading-spinner {
                width: 16px;
                height: 16px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-top: 2px solid rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
        }

        .no-more {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.4);
            gap: 15px;

            .no-more-line {
                flex: 1;
                height: 1px;
                background: rgba(255, 255, 255, 0.1);
            }
        }
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style>
