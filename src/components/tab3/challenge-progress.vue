<template>
    <div
        ref="progressRef"
        class="target-progress relative box-border h-100 w-fit">
        <div class="progress-bar absolute left-0 top-20 z-0 h-[7.5px] w-full flex items-center rounded-[1px] px-1.5">
            <div
                class="progress-value h-full"
                :class="{ 'min-w-6': !!progressValueWidth }"
                :style="{ width: `${progressValueWidth}` }"></div>
        </div>
        <div class="points relative z-10 w-fit flex items-center pb-70 pt-15">
            <div
                v-for="target in targetsStatus"
                :key="target.value"
                ref="points"
                class="level-point relative ml-47 h-[16px] w-[16px] flex shrink-0 flex-col items-center justify-center first:ml-69">
                <div class="target-text absolute top-20">{{ omitValue(target.value) }}</div>
                <img
                    :src="target.done ? doneIcon : unDoneIcon"
                    class="box-border h-full w-full"
                    alt="">
                <div class="left-1/2 box-border pt-8.7 !absolute -top-52 -translate-x-1/2">
                    <div class="reward box-border h-[32.5px] w-[33px] p-3">
                        <img
                            :src="target.rewards?.[0]?.imageUrl || target.icon"
                            class="h-full w-full object-contain"
                            alt="">
                        <div
                            v-if="target.rewards?.[0]?.num"
                            class="num absolute left-1/2 flex items-center justify-center whitespace-nowrap text-center -bottom-6 -translate-x-1/2">
                            x{{ target.rewards[0].num }}{{ target.rewards[0].unit }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { scrollToAnchor } from '@/utils';

// Props 定义
const props = defineProps({
    // 目标节点数据
    targets: {
        type: Array,
        default: () => [
            {
                value: 1,
                rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 1 }],
            },
            {
                value: 3,
                rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 2 }],
            },
            {
                value: 5,
                rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 2 }],
            },
        ],
    },
    // 当前进度值
    value: {
        type: Number,
        default: 0,
    },
    // 已完成节点图标
    doneIcon: {
        type: String,
        default: () => 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250730145255_38667834.png',
    },
    // 未完成节点图标
    unDoneIcon: {
        type: String,
        default: () => 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250730145231_24402573.png',
    },
});

const points = ref(null);
const progressRef = ref(null);
const progressValueWidth = ref(0);

const targetsStatus = computed(() => {
    return props.targets.map(target => ({
        ...target,
        done: props.value >= target.value,
    }));
});

// 计算进度条宽度
const computeProgressValue = () => {
    if (!props.value) {
        progressValueWidth.value = 0;
        return;
    }

    // 找到当前等级
    const currentValue = props.value;
    const lastCompletedTargetIndex = targetsStatus.value.findLastIndex(target => target.done);

    // 如果已达到最高等级
    if (lastCompletedTargetIndex === targetsStatus.value.length - 1) {
        progressValueWidth.value = '100%';
        return;
    }

    // 获取最后一个已完成节点相对于父容器的偏移量
    const currentWidth = points.value[lastCompletedTargetIndex]?.offsetLeft || 0;
    const pointWidth = points.value[0]?.offsetWidth || 0; // 每个节点的宽度

    // 计算当前进度位置
    const lastTarget = targetsStatus.value[lastCompletedTargetIndex] || { value: 0 };
    const nextTarget = targetsStatus.value[lastCompletedTargetIndex + 1];

    const nextPointElement = points.value[lastCompletedTargetIndex + 1];
    const nextWidth = nextPointElement?.offsetLeft || 0;

    const lastTargetValue = lastTarget.value || 0;
    const nextTargetValue = nextTarget?.value || 0;

    // 如果没有下一级或者当前值不足
    if (!nextTarget || currentValue <= lastTargetValue) {
        progressValueWidth.value = `${currentWidth + pointWidth / 2}px`;
        return;
    }

    // 计算进度比例
    const progress = Math.min(1, (currentValue - lastTargetValue) / (nextTargetValue - lastTargetValue));
    const restWidth = progress * (nextWidth - currentWidth);

    progressValueWidth.value = `${currentWidth + restWidth + pointWidth / 2}px`;
};

// 焦点到当前进度
const focus = () => {
    const lastCompletedTargetIndex = targetsStatus.value.findLastIndex(target => target.done);
    const lastCompletedElement = points.value[lastCompletedTargetIndex];
    if (!lastCompletedElement)
        return;
    const parentElement = progressRef.value.parentElement;
    if (parentElement) {
        // scrollIntoViewMiddle(lastCompletedElement, parentElement);
        scrollToAnchor(parentElement, lastCompletedElement, 0, 'x');
    }
};

// 监听值变化
const progressKey = computed(() => {
    return `${props.value}_${targetsStatus.value.length}_${targetsStatus.value.filter(t => t.done).length}`;
});

onMounted(() => {
    watch(() => progressKey.value, () => {
        nextTick(() => {
            computeProgressValue();
            focus();
        });
    }, { immediate: true });
});

// 暴露方法给父组件
defineExpose({
    computeProgressValue,
    focus,
});
</script>

<style lang="less" scoped>
.progress-bar {
    background-image: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    padding-right: 4px;
    .progress-value {
        .point-9-px(url('@/assets/img/tab3/<EMAIL>'),103,5, 0, 2, 0, 2,2);
        border-top: 0;
        border-bottom: 0;
        border-left: 0;
        border-right: 0;
        height: 5px;
    }
}
.target-text {
    font-size: 11px;
    font-family:
        Alibaba PuHuiTi,
        Alibaba PuHuiTi-Regular;
    font-weight: 400;
    text-align: center;
    color: #ffebc7;
    line-height: 13px;
}
.reward {
    background-image: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    .num {
        width: 29px;
        height: 11.5px;
        background: #c79e4d;
        border: 1px solid #6c4525;
        border-radius: 5px;
        font-size: 9px;
        font-family:
            Alibaba PuHuiTi,
            Alibaba PuHuiTi-Regular;
        font-weight: 400;
        text-align: center;
        color: #ffffff;
        line-height: 11px;
    }
}
</style>
