<template>
    <div class="challenge-progress w-full">
        <!-- 累计天数显示 -->
        <div class="mb-4 flex items-center justify-center">
            <div class="mr-2 text-[14px] text-[#ffebc7]">已累计参与：</div>
            <div class="text-[18px] text-[#ffeb3b] font-bold">{{ value || 0 }}</div>
            <div class="ml-1 text-[14px] text-[#ffebc7]">天</div>
        </div>

        <!-- 奖励节点进度 -->
        <div class="relative">
            <!-- 进度条背景 -->
            <div class="progress-track absolute left-0 top-1/2 h-[4px] w-full transform rounded-full bg-[#333] -translate-y-1/2"></div>

            <!-- 进度条填充 -->
            <div
                class="progress-fill absolute left-0 top-1/2 h-[4px] transform rounded-full from-[#ffeb3b] to-[#ff9800] bg-gradient-to-r transition-all duration-500 -translate-y-1/2"
                :style="{ width: `${progressPercentage}%` }"
            ></div>

            <!-- 奖励节点 -->
            <div class="relative flex items-center justify-between">
                <div
                    v-for="(reward, index) in rewards"
                    :key="index"
                    class="relative flex flex-col items-center"
                    :class="{ 'opacity-50': !isRewardUnlocked(reward.days) }"
                >
                    <!-- 节点圆圈 -->
                    <div
                        class="relative z-10 mb-2 h-[40px] w-[40px] flex items-center justify-center rounded-full"
                        :class="isRewardUnlocked(reward.days) ? 'bg-[#ffeb3b]' : 'bg-[#666]'"
                    >
                        <!-- 奖励图标 -->
                        <img
                            :src="requireImg(reward.icon)"
                            class="h-[24px] w-[24px]"
                            :alt="reward.name"
                        />

                        <!-- 完成标识 -->
                        <div
                            v-if="isRewardUnlocked(reward.days)"
                            class="absolute h-[14px] w-[14px] flex items-center justify-center rounded-full bg-[#4caf50] -right-1 -top-1"
                        >
                            <div class="text-[8px] text-white">✓</div>
                        </div>
                    </div>

                    <!-- 天数标识 -->
                    <div
                        class="mb-1 text-center text-[10px]"
                        :class="isRewardUnlocked(reward.days) ? 'text-[#ffeb3b]' : 'text-[#999]'"
                    >
                        {{ reward.days }}天
                    </div>

                    <!-- 奖励名称 -->
                    <div
                        class="w-[50px] text-center text-[9px]"
                        :class="isRewardUnlocked(reward.days) ? 'text-[#ffebc7]' : 'text-[#999]'"
                    >
                        {{ reward.name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { getRewardInfo } from '@/utils';

// Props 定义
const props = defineProps({
    // 当前参与天数
    value: {
        type: Number,
        default: 0,
    },
    // 奖励配置
    rewards: {
        type: Array,
        default: () => [
            { days: 1, name: '金币奖励', ...getRewardInfo('A24') },
            { days: 3, name: '钻石奖励', ...getRewardInfo('A25') },
            { days: 5, name: '特权卡', ...getRewardInfo('A26') },
        ],
    },

});

// 计算进度百分比
const progressPercentage = computed(() => {
    if (!props.value || props.rewards.length === 0)
        return 0;

    const maxDays = Math.max(...props.rewards.map(r => r.days));
    const percentage = Math.min((props.value / maxDays) * 100, 100);

    // 确保至少有一些进度显示
    return Math.max(percentage, props.value > 0 ? 5 : 0);
});

// 判断奖励是否解锁
function isRewardUnlocked(days) {
    return props.value >= days;
}

// 暴露方法给父组件
defineExpose({
    isRewardUnlocked,
});
</script>

<style lang="less" scoped>
.challenge-progress {
    .progress-track {
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .progress-fill {
        box-shadow: 0 1px 3px rgba(255, 235, 59, 0.4);
    }
}
</style>
