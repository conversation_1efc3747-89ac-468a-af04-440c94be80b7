// 执行pb.cjs命令后会覆盖掉手写的代码，请谨慎操作!
import { drawHttp } from '../utils/request';
import getDrawMockData from './drawMockData';

export const fetchApi = ({ proPrefix = '/activity.Activity/', api, data = {}, config = {} }) => {
    const { mock } = myWebview.params;
    if (mock)
        return to(getDrawMockData(api, data));

    const url = `${proPrefix}/${api}`.replace('//', '/');
    return to(drawHttp.post(url, data, config));
};

/**
 * ********************************
 * *********  活动接口 *************
 * ********************************
 */

const REQUEST_API_MAP = {
    getHeartbeatChallengeData: 'getHeartbeatChallengeData',
    setScore: 'setScore',
    getRank: 'getRank',
};

export const getHeartbeatChallengeData = async payload => fetchApi(REQUEST_API_MAP.getHeartbeatChallengeData, payload);

export const setScore = async payload => fetchApi(REQUEST_API_MAP.setScore, payload);

export const getRank = async payload => fetchApi(REQUEST_API_MAP.getRank, payload);
