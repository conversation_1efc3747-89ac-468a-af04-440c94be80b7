<template>
    <div class="progress-example">
        <h3>进度条组件使用示例</h3>
        
        <!-- 基础用法 -->
        <div class="example-section">
            <h4>基础用法</h4>
            <div class="progress-container box-border w-300 overflow-x-auto">
                <LevelProgress
                    :value="currentValue"
                    :targets="basicTargets"
                />
            </div>
            <div class="controls">
                <button @click="updateValue(0)">重置</button>
                <button @click="updateValue(150)">150</button>
                <button @click="updateValue(350)">350</button>
                <button @click="updateValue(700)">700</button>
                <button @click="updateValue(1500)">1500</button>
            </div>
            <p>当前值: {{ currentValue }}</p>
        </div>

        <!-- 自定义图标 -->
        <div class="example-section">
            <h4>自定义图标</h4>
            <div class="progress-container box-border w-300 overflow-x-auto">
                <LevelProgress
                    :value="currentValue"
                    :targets="customTargets"
                    done-icon="https://obs-cdn.52tt.com/tt/fe-moss/web/20250730145255_38667834.png"
                    un-done-icon="https://obs-cdn.52tt.com/tt/fe-moss/web/20250730145231_24402573.png"
                />
            </div>
        </div>

        <!-- 星动挑战样式 -->
        <div class="example-section">
            <h4>星动挑战进度</h4>
            <div class="progress-container box-border w-300 overflow-x-auto">
                <LevelProgress
                    :value="challengeValue"
                    :targets="challengeTargets"
                    :done-icon="requireImg('tab3/<EMAIL>')"
                    :un-done-icon="requireImg('tab3/<EMAIL>')"
                />
            </div>
            <div class="controls">
                <button @click="challengeValue = 0">0天</button>
                <button @click="challengeValue = 3">3天</button>
                <button @click="challengeValue = 7">7天</button>
                <button @click="challengeValue = 15">15天</button>
                <button @click="challengeValue = 30">30天</button>
            </div>
            <p>已参与天数: {{ challengeValue }}</p>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import LevelProgress from './level-progress.vue';

// 基础示例数据
const currentValue = ref(150);
const basicTargets = ref([
    {
        value: 100,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 1 }],
    },
    {
        value: 300,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 2 }],
    },
    {
        value: 600,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 3 }],
    },
    {
        value: 1000,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 5 }],
    },
]);

// 自定义示例数据
const customTargets = ref([
    {
        value: 50,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 1 }],
    },
    {
        value: 200,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 3 }],
    },
    {
        value: 500,
        rewards: [{ imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20250729160647_87205272.png', num: 5 }],
    },
]);

// 星动挑战示例数据
const challengeValue = ref(5);
const challengeTargets = ref([
    {
        value: 3,
        rewards: [{ imageUrl: requireImg('tab3/<EMAIL>'), num: 100 }],
        icon: requireImg('tab3/<EMAIL>'),
    },
    {
        value: 7,
        rewards: [{ imageUrl: requireImg('tab3/<EMAIL>'), num: 50 }],
        icon: requireImg('tab3/<EMAIL>'),
    },
    {
        value: 15,
        rewards: [{ imageUrl: requireImg('tab3/<EMAIL>'), num: 1 }],
        icon: requireImg('tab3/<EMAIL>'),
    },
    {
        value: 30,
        rewards: [{ imageUrl: requireImg('tab3/<EMAIL>'), num: 1 }],
        icon: requireImg('tab3/<EMAIL>'),
    },
]);

// 更新数值
function updateValue(value) {
    currentValue.value = value;
}
</script>

<style lang="less" scoped>
.progress-example {
    padding: 20px;
    
    h3 {
        color: #333;
        margin-bottom: 20px;
    }
    
    .example-section {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        
        h4 {
            color: #666;
            margin-bottom: 15px;
        }
        
        .progress-container {
            margin-bottom: 15px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            
            button {
                padding: 5px 10px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                
                &:hover {
                    background: #0056b3;
                }
            }
        }
        
        p {
            color: #666;
            font-size: 14px;
        }
    }
}
</style>
